import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import { SupabaseService } from './SupabaseService.js';
import { Trade, TradeStatus } from './ExecutionService.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export interface StrategyPerformanceRecord {
  id?: string;
  strategy_type: string;
  strategy_variant?: string;
  trade_id?: string;
  opportunity_id: string;
  execution_parameters: {
    gas_limit: number;
    slippage_tolerance: number;
    position_size: number;
    max_execution_time: number;
  };
  market_conditions: {
    volatility: number;
    liquidity: number;
    network_congestion: number;
    gas_price: number;
    market_regime: string;
  };
  success: boolean;
  profit_loss: number;
  execution_time_ms: number;
  gas_cost: number;
  slippage_actual?: number;
  confidence_score: number;
  risk_score: number;
  network: string;
  timestamp: string;
}

export interface StrategyWeight {
  strategy_type: string;
  strategy_variant?: string;
  network: string;
  weight: number;
  success_rate: number;
  avg_profit: number;
  avg_risk_adjusted_return: number;
  total_executions: number;
  recent_performance_score: number;
  market_regime: string;
  last_updated: string;
}

export interface MarketRegime {
  regime_name: string;
  volatility_min: number;
  volatility_max: number;
  liquidity_min: number;
  network_congestion_min: number;
  network_congestion_max: number;
  description: string;
  is_active: boolean;
}

export interface LearningEvent {
  event_type: 'weight_update' | 'strategy_disabled' | 'new_variant_tested' | 'regime_change';
  strategy_type: string;
  strategy_variant?: string;
  old_weight?: number;
  new_weight?: number;
  reason: string;
  confidence: number;
  network: string;
  market_regime?: string;
  metadata?: any;
}

export class MLLearningService extends EventEmitter {
  private supabaseService: SupabaseService;
  private isRunning = false;
  private learningInterval: NodeJS.Timeout | null = null;
  private strategyWeights = new Map<string, StrategyWeight>();
  private marketRegimes: MarketRegime[] = [];
  private currentMarketRegime = 'normal';
  
  // Learning parameters
  private readonly LEARNING_RATE = 0.1;
  private readonly MIN_SAMPLES_FOR_LEARNING = 10;
  private readonly PERFORMANCE_WINDOW_HOURS = 24;
  private readonly WEIGHT_DECAY_FACTOR = 0.95;
  private readonly MIN_WEIGHT = 0.1;
  private readonly MAX_WEIGHT = 2.0;

  constructor(supabaseService: SupabaseService) {
    super();
    this.supabaseService = supabaseService;
    this.initializeMarketRegimes();
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting ML Learning Service...');
    this.isRunning = true;

    // Load existing strategy weights
    await this.loadStrategyWeights();

    // Start learning loop
    this.learningInterval = setInterval(() => {
      this.performLearningCycle();
    }, 60000); // Learn every minute

    // Initial learning cycle
    await this.performLearningCycle();

    logger.info('ML Learning Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping ML Learning Service...');
    this.isRunning = false;

    if (this.learningInterval) {
      clearInterval(this.learningInterval);
      this.learningInterval = null;
    }

    logger.info('ML Learning Service stopped');
  }

  public async recordStrategyPerformance(
    trade: Trade,
    opportunity: ArbitrageOpportunity,
    executionParameters: any,
    marketConditions: any
  ) {
    try {
      const performanceRecord: StrategyPerformanceRecord = {
        strategy_type: trade.type,
        strategy_variant: 'default', // Can be extended for A/B testing
        trade_id: trade.id,
        opportunity_id: trade.opportunityId,
        execution_parameters: executionParameters,
        market_conditions: {
          ...marketConditions,
          market_regime: this.currentMarketRegime
        },
        success: trade.status === TradeStatus.SUCCESS,
        profit_loss: trade.executedProfit,
        execution_time_ms: trade.executionTime || 0,
        gas_cost: trade.gasFees,
        confidence_score: opportunity.confidence || 50,
        risk_score: this.calculateRiskScore(trade, opportunity),
        network: trade.network,
        timestamp: new Date(trade.timestamp).toISOString()
      };

      // Save to database
      await this.saveStrategyPerformance(performanceRecord);

      // Trigger immediate learning if significant event
      if (this.isSignificantEvent(performanceRecord)) {
        await this.performLearningCycle();
      }

      logger.debug(`Strategy performance recorded for trade ${trade.id}`);
    } catch (error) {
      logger.error('Error recording strategy performance:', error);
    }
  }

  public getStrategyWeight(strategyType: string, network: string, variant = 'default'): number {
    const key = `${strategyType}_${variant}_${network}_${this.currentMarketRegime}`;
    const weight = this.strategyWeights.get(key);
    return weight?.weight || 1.0;
  }

  public getStrategyWeights(): Map<string, StrategyWeight> {
    return new Map(this.strategyWeights);
  }

  public getCurrentMarketRegime(): string {
    return this.currentMarketRegime;
  }

  private async performLearningCycle() {
    try {
      logger.debug('Performing ML learning cycle...');

      // Update market regime
      await this.updateMarketRegime();

      // Analyze recent performance
      const recentPerformance = await this.getRecentPerformance();

      // Update strategy weights based on performance
      await this.updateStrategyWeights(recentPerformance);

      // Emit learning update
      this.emit('learningUpdate', {
        timestamp: Date.now(),
        marketRegime: this.currentMarketRegime,
        strategyWeights: Array.from(this.strategyWeights.values()),
        totalStrategies: this.strategyWeights.size
      });

    } catch (error) {
      logger.error('Error in learning cycle:', error);
    }
  }

  private async updateMarketRegime() {
    // Simplified market regime detection
    // In production, this would use more sophisticated analysis
    const marketData = await this.getCurrentMarketData();
    
    const newRegime = this.classifyMarketRegime(marketData);
    
    if (newRegime !== this.currentMarketRegime) {
      const oldRegime = this.currentMarketRegime;
      this.currentMarketRegime = newRegime;
      
      await this.logLearningEvent({
        event_type: 'regime_change',
        strategy_type: 'system',
        reason: `Market regime changed from ${oldRegime} to ${newRegime}`,
        confidence: 80,
        network: 'all',
        market_regime: newRegime,
        metadata: { old_regime: oldRegime, market_data: marketData }
      });

      logger.info(`Market regime changed: ${oldRegime} -> ${newRegime}`);
    }
  }

  private classifyMarketRegime(marketData: any): string {
    const { volatility, liquidity, networkCongestion } = marketData;

    if (volatility > 30 || networkCongestion > 80) {
      return 'high_volatility';
    } else if (liquidity < 1000000 || networkCongestion > 60) {
      return 'low_liquidity';
    } else if (volatility < 5 && networkCongestion < 20) {
      return 'stable';
    }
    
    return 'normal';
  }

  private async getCurrentMarketData() {
    // Simplified market data - in production would fetch real data
    return {
      volatility: Math.random() * 40,
      liquidity: 1000000 + Math.random() * 5000000,
      networkCongestion: Math.random() * 100
    };
  }

  private calculateRiskScore(trade: Trade, opportunity: ArbitrageOpportunity): number {
    // Simplified risk calculation
    let riskScore = 50; // Base risk

    // Adjust based on trade size
    if (trade.executedProfit > 1000) riskScore += 20;
    if (trade.executedProfit < 100) riskScore -= 10;

    // Adjust based on gas fees ratio
    const gasFeeRatio = trade.gasFees / Math.abs(trade.executedProfit);
    if (gasFeeRatio > 0.5) riskScore += 30;
    if (gasFeeRatio < 0.1) riskScore -= 10;

    // Adjust based on opportunity type
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) riskScore += 15;
    if (opportunity.type === ArbitrageType.TRIANGULAR) riskScore += 10;

    return Math.max(0, Math.min(100, riskScore));
  }

  private isSignificantEvent(performance: StrategyPerformanceRecord): boolean {
    // Determine if this performance record represents a significant event
    return (
      Math.abs(performance.profit_loss) > 500 || // Large profit/loss
      performance.execution_time_ms > 30000 || // Long execution time
      !performance.success // Failed trade
    );
  }

  private async saveStrategyPerformance(record: StrategyPerformanceRecord) {
    if (!this.supabaseService.isHealthy()) {
      logger.warn('Supabase not available, skipping strategy performance save');
      return;
    }

    try {
      const { error } = await this.supabaseService['supabase']
        .from('strategy_performance')
        .insert([record]);

      if (error) {
        logger.error('Error saving strategy performance:', error);
      }
    } catch (error) {
      logger.error('Failed to save strategy performance:', error);
    }
  }

  private async loadStrategyWeights() {
    if (!this.supabaseService.isHealthy()) {
      logger.warn('Supabase not available, using default strategy weights');
      return;
    }

    try {
      const { data, error } = await this.supabaseService['supabase']
        .from('strategy_weights')
        .select('*')
        .eq('is_active', true);

      if (error) {
        logger.error('Error loading strategy weights:', error);
        return;
      }

      if (data) {
        data.forEach((weight: any) => {
          const key = `${weight.strategy_type}_${weight.strategy_variant || 'default'}_${weight.network}_${weight.market_regime}`;
          this.strategyWeights.set(key, weight);
        });
        logger.info(`Loaded ${data.length} strategy weights`);
      }
    } catch (error) {
      logger.error('Failed to load strategy weights:', error);
    }
  }

  private async getRecentPerformance(): Promise<StrategyPerformanceRecord[]> {
    if (!this.supabaseService.isHealthy()) {
      return [];
    }

    try {
      const cutoffTime = new Date(Date.now() - this.PERFORMANCE_WINDOW_HOURS * 60 * 60 * 1000);

      const { data, error } = await this.supabaseService['supabase']
        .from('strategy_performance')
        .select('*')
        .gte('timestamp', cutoffTime.toISOString())
        .order('timestamp', { ascending: false });

      if (error) {
        logger.error('Error fetching recent performance:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Failed to fetch recent performance:', error);
      return [];
    }
  }

  private async updateStrategyWeights(recentPerformance: StrategyPerformanceRecord[]) {
    const strategyGroups = this.groupPerformanceByStrategy(recentPerformance);

    for (const [strategyKey, performances] of strategyGroups.entries()) {
      if (performances.length < this.MIN_SAMPLES_FOR_LEARNING) {
        continue; // Not enough data for learning
      }

      const currentWeight = this.strategyWeights.get(strategyKey)?.weight || 1.0;
      const newWeight = this.calculateNewWeight(performances, currentWeight);

      if (Math.abs(newWeight - currentWeight) > 0.05) { // Significant change
        await this.updateStrategyWeight(strategyKey, performances[0], currentWeight, newWeight);
      }
    }
  }

  private groupPerformanceByStrategy(performances: StrategyPerformanceRecord[]): Map<string, StrategyPerformanceRecord[]> {
    const groups = new Map<string, StrategyPerformanceRecord[]>();

    performances.forEach(perf => {
      const key = `${perf.strategy_type}_${perf.strategy_variant || 'default'}_${perf.network}_${perf.market_conditions.market_regime}`;

      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(perf);
    });

    return groups;
  }

  private calculateNewWeight(performances: StrategyPerformanceRecord[], currentWeight: number): number {
    const successRate = performances.filter(p => p.success).length / performances.length;
    const avgProfit = performances.reduce((sum, p) => sum + p.profit_loss, 0) / performances.length;
    const avgRisk = performances.reduce((sum, p) => sum + p.risk_score, 0) / performances.length;

    // Risk-adjusted return
    const riskAdjustedReturn = avgProfit / (avgRisk / 100 + 0.1); // Avoid division by zero

    // Performance score (0-100)
    const performanceScore = (successRate * 50) + (Math.max(0, riskAdjustedReturn) / 100 * 50);

    // Calculate weight adjustment
    const targetWeight = 0.5 + (performanceScore / 100) * 1.5; // Range: 0.5 to 2.0

    // Apply learning rate and constraints
    let newWeight = currentWeight + this.LEARNING_RATE * (targetWeight - currentWeight);
    newWeight = Math.max(this.MIN_WEIGHT, Math.min(this.MAX_WEIGHT, newWeight));

    return newWeight;
  }

  private async updateStrategyWeight(
    strategyKey: string,
    samplePerformance: StrategyPerformanceRecord,
    oldWeight: number,
    newWeight: number
  ) {
    const [strategyType, variant, network, marketRegime] = strategyKey.split('_');

    const weightRecord: StrategyWeight = {
      strategy_type: strategyType,
      strategy_variant: variant === 'default' ? undefined : variant,
      network: network,
      weight: newWeight,
      success_rate: 0, // Will be calculated in database
      avg_profit: 0, // Will be calculated in database
      avg_risk_adjusted_return: 0, // Will be calculated in database
      total_executions: 0, // Will be calculated in database
      recent_performance_score: (newWeight - 0.5) / 1.5 * 100, // Convert back to score
      market_regime: marketRegime,
      last_updated: new Date().toISOString()
    };

    // Update local cache
    this.strategyWeights.set(strategyKey, weightRecord);

    // Save to database
    await this.saveStrategyWeight(weightRecord);

    // Log learning event
    await this.logLearningEvent({
      event_type: 'weight_update',
      strategy_type: strategyType,
      strategy_variant: variant === 'default' ? undefined : variant,
      old_weight: oldWeight,
      new_weight: newWeight,
      reason: `Performance-based weight adjustment`,
      confidence: 75,
      network: network,
      market_regime: marketRegime
    });

    logger.info(`Updated strategy weight: ${strategyKey} ${oldWeight.toFixed(3)} -> ${newWeight.toFixed(3)}`);
  }

  private async saveStrategyWeight(weight: StrategyWeight) {
    if (!this.supabaseService.isHealthy()) {
      return;
    }

    try {
      const { error } = await this.supabaseService['supabase']
        .from('strategy_weights')
        .upsert([weight], {
          onConflict: 'strategy_type,strategy_variant,network,market_regime'
        });

      if (error) {
        logger.error('Error saving strategy weight:', error);
      }
    } catch (error) {
      logger.error('Failed to save strategy weight:', error);
    }
  }

  private async logLearningEvent(event: LearningEvent) {
    if (!this.supabaseService.isHealthy()) {
      return;
    }

    try {
      const { error } = await this.supabaseService['supabase']
        .from('strategy_learning_events')
        .insert([{
          ...event,
          created_at: new Date().toISOString()
        }]);

      if (error) {
        logger.error('Error logging learning event:', error);
      }
    } catch (error) {
      logger.error('Failed to log learning event:', error);
    }
  }

  private async initializeMarketRegimes() {
    this.marketRegimes = [
      {
        regime_name: 'stable',
        volatility_min: 0,
        volatility_max: 5,
        liquidity_min: 2000000,
        network_congestion_min: 0,
        network_congestion_max: 20,
        description: 'Low volatility, high liquidity, low congestion',
        is_active: true
      },
      {
        regime_name: 'normal',
        volatility_min: 5,
        volatility_max: 20,
        liquidity_min: 1000000,
        network_congestion_min: 20,
        network_congestion_max: 60,
        description: 'Normal market conditions',
        is_active: true
      },
      {
        regime_name: 'high_volatility',
        volatility_min: 20,
        volatility_max: 100,
        liquidity_min: 500000,
        network_congestion_min: 60,
        network_congestion_max: 100,
        description: 'High volatility or network congestion',
        is_active: true
      },
      {
        regime_name: 'low_liquidity',
        volatility_min: 0,
        volatility_max: 100,
        liquidity_min: 0,
        network_congestion_min: 0,
        network_congestion_max: 100,
        description: 'Low liquidity conditions',
        is_active: true
      }
    ];
  }

  // Public methods for external access
  public async getStrategyPerformanceSummary() {
    if (!this.supabaseService.isHealthy()) {
      return null;
    }

    try {
      const { data, error } = await this.supabaseService['supabase']
        .from('strategy_performance_summary')
        .select('*')
        .order('success_rate', { ascending: false });

      if (error) {
        logger.error('Error fetching strategy performance summary:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Failed to fetch strategy performance summary:', error);
      return null;
    }
  }

  public async getLearningEvents(limit = 50) {
    if (!this.supabaseService.isHealthy()) {
      return [];
    }

    try {
      const { data, error } = await this.supabaseService['supabase']
        .from('strategy_learning_events')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Error fetching learning events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Failed to fetch learning events:', error);
      return [];
    }
  }
}
