import { z } from 'zod';

const configSchema = z.object({
  // Server Configuration
  PORT: z.string().default('3001'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),

  // Database Configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  DATABASE_URL: z.string().optional(),

  // Supabase Configuration
  SUPABASE_URL: z.string().optional(),
  SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  SUPABASE_JWT_SECRET: z.string().optional(),
  SUPABASE_DATABASE_URL: z.string().optional(),

  // InfluxDB Configuration
  INFLUXDB_URL: z.string().default('http://localhost:8086'),
  INFLUXDB_TOKEN: z.string().optional(),
  INFLUXDB_ORG: z.string().default('mev-arbitrage-org'),
  INFLUXDB_BUCKET: z.string().default('mev-arbitrage-metrics'),
  INFLUXDB_USERNAME: z.string().optional(),
  INFLUXDB_PASSWORD: z.string().optional(),

  // Blockchain Configuration
  ETHEREUM_RPC_URL: z.string().default('https://eth-mainnet.alchemyapi.io/v2/your-api-key'),
  POLYGON_RPC_URL: z.string().default('https://polygon-mainnet.alchemyapi.io/v2/your-api-key'),
  BSC_RPC_URL: z.string().default('https://bsc-dataseed.binance.org/'),
  SOLANA_RPC_URL: z.string().default('https://api.mainnet-beta.solana.com'),

  // Private Keys (for transaction signing)
  PRIVATE_KEY: z.string().optional(),
  FLASHBOTS_PRIVATE_KEY: z.string().optional(),

  // External API Keys
  CHAINLINK_API_KEY: z.string().optional(),
  PYTH_API_KEY: z.string().optional(),
  COINGECKO_API_KEY: z.string().optional(),
  ETHERSCAN_API_KEY: z.string().optional(),
  POLYGONSCAN_API_KEY: z.string().optional(),
  BSCSCAN_API_KEY: z.string().optional(),
  ALCHEMY_API_KEY: z.string().optional(),
  INFURA_PROJECT_ID: z.string().optional(),
  MORALIS_API_KEY: z.string().optional(),

  // Trading Configuration
  MIN_PROFIT_THRESHOLD: z.string().default('50'), // USD
  MAX_POSITION_SIZE: z.string().default('10000'), // USD
  MAX_SLIPPAGE: z.string().default('0.5'), // percentage
  GAS_PRICE_MULTIPLIER: z.string().default('1.1'),

  // Pre-Execution Validation Configuration
  ENABLE_MANDATORY_SIMULATION: z.string().default('true'),
  MIN_PROFIT_MARGIN_BUFFER: z.string().default('10'), // percentage above break-even
  MAX_SIMULATION_TIME: z.string().default('5000'), // milliseconds
  SIMULATION_SLIPPAGE_BUFFER: z.string().default('0.2'), // additional slippage buffer
  ENABLE_BRIDGE_FEE_CALCULATION: z.string().default('true'),
  ENABLE_NETWORK_CONGESTION_CHECK: z.string().default('true'),

  // Risk Management
  EMERGENCY_STOP: z.string().default('false'),
  MAX_DAILY_LOSS: z.string().default('1000'), // USD
  POSITION_SIZE_PERCENTAGE: z.string().default('2'), // percentage of total capital

  // MEV Protection Configuration
  ENABLE_MEV_PROTECTION: z.string().default('true'),
  FLASHBOTS_RELAY_URL: z.string().default('https://relay.flashbots.net'),
  FLASHBOTS_BUILDER_URL: z.string().default('https://builder.flashbots.net'),
  FLASHBOTS_PROTECT_URL: z.string().default('https://protect.flashbots.net'),
  MEV_PROTECTION_TIMEOUT: z.string().default('30000'), // milliseconds
  ENABLE_PRIVATE_MEMPOOL: z.string().default('true'),
  DYNAMIC_GAS_PRICING: z.string().default('true'),
  MEV_PROTECTION_FALLBACK: z.string().default('true'),

  // Telegram Bot Configuration
  TELEGRAM_BOT_TOKEN: z.string().optional(),
  TELEGRAM_CHAT_ID: z.string().optional(),

  // Email Notifications
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().default('587'),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  ALERT_EMAIL: z.string().optional(),

  // Security
  JWT_SECRET: z.string().optional(),
  API_RATE_LIMIT_WINDOW: z.string().default('900000'), // 15 minutes
  API_RATE_LIMIT_MAX: z.string().default('100'),
  CORS_ORIGIN: z.string().default('http://localhost:5173,http://localhost:3000'),

  // Monitoring
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  ENABLE_METRICS: z.string().default('true'),
});

export type Config = z.infer<typeof configSchema>;

export const config: Config = configSchema.parse(process.env);

export default config;
