import logger from '../utils/logger.js';
import config from '../config/index.js';

// Import all services
import { PriceFeedService } from './PriceFeedService.js';
import { TokenDiscoveryService } from './TokenDiscoveryService.js';
import { OpportunityDetectionService } from './OpportunityDetectionService.js';
import { ExecutionService } from './ExecutionService.js';
import { MLLearningService } from './MLLearningService.js';
import { StrategySelectionService } from './StrategySelectionService.js';
import { RiskManagementService } from './RiskManagementService.js';
import { PreExecutionValidationService } from './PreExecutionValidationService.js';
import { MEVProtectionService } from './MEVProtectionService.js';
import { MultiChainService } from './MultiChainService.js';
import { SupabaseService } from './SupabaseService.js';

export interface ServiceIntegrationConfig {
  enablePreExecutionValidation: boolean;
  enableMEVProtection: boolean;
  enableMLLearning: boolean;
  enableRiskManagement: boolean;
}

export class ServiceIntegrator {
  private services: Map<string, any> = new Map();
  private isInitialized = false;
  private config: ServiceIntegrationConfig;

  constructor(integrationConfig?: Partial<ServiceIntegrationConfig>) {
    this.config = {
      enablePreExecutionValidation: config.ENABLE_MANDATORY_SIMULATION === 'true',
      enableMEVProtection: config.ENABLE_MEV_PROTECTION === 'true',
      enableMLLearning: true,
      enableRiskManagement: true,
      ...integrationConfig
    };
  }

  /**
   * Initialize all services and wire them together
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      logger.warn('ServiceIntegrator already initialized');
      return;
    }

    try {
      logger.info('Initializing MEV Arbitrage Bot services...');

      // Initialize core services first
      await this.initializeCoreServices();

      // Initialize enhancement services
      await this.initializeEnhancementServices();

      // Wire services together
      await this.wireServices();

      // Start all services
      await this.startServices();

      this.isInitialized = true;
      logger.info('All services initialized and integrated successfully');

    } catch (error) {
      logger.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Initialize core services required for basic functionality
   */
  private async initializeCoreServices(): Promise<void> {
    logger.info('Initializing core services...');

    // Supabase Service (database)
    const supabaseService = new SupabaseService();
    this.services.set('supabase', supabaseService);

    // Multi-Chain Service
    const multiChainService = new MultiChainService();
    this.services.set('multiChain', multiChainService);

    // Price Feed Service
    const priceFeedService = new PriceFeedService();
    this.services.set('priceFeed', priceFeedService);

    // Token Discovery Service
    const tokenDiscoveryService = new TokenDiscoveryService();
    this.services.set('tokenDiscovery', tokenDiscoveryService);

    // Opportunity Detection Service
    const opportunityDetectionService = new OpportunityDetectionService(
      priceFeedService,
      tokenDiscoveryService
    );
    this.services.set('opportunityDetection', opportunityDetectionService);

    // Execution Service
    const executionService = new ExecutionService();
    this.services.set('execution', executionService);

    logger.info('Core services initialized');
  }

  /**
   * Initialize enhancement services (validation, MEV protection, ML, etc.)
   */
  private async initializeEnhancementServices(): Promise<void> {
    logger.info('Initializing enhancement services...');

    // Pre-Execution Validation Service
    if (this.config.enablePreExecutionValidation) {
      const preExecutionValidationService = new PreExecutionValidationService();
      this.services.set('preExecutionValidation', preExecutionValidationService);
      logger.info('Pre-Execution Validation Service enabled');
    }

    // MEV Protection Service
    if (this.config.enableMEVProtection) {
      const mevProtectionService = new MEVProtectionService();
      this.services.set('mevProtection', mevProtectionService);
      logger.info('MEV Protection Service enabled');
    }

    // ML Learning Service
    if (this.config.enableMLLearning) {
      const supabaseService = this.services.get('supabase');
      const mlLearningService = new MLLearningService(supabaseService);
      this.services.set('mlLearning', mlLearningService);

      // Strategy Selection Service (depends on ML)
      const strategySelectionService = new StrategySelectionService(mlLearningService);
      this.services.set('strategySelection', strategySelectionService);
      
      logger.info('ML Learning and Strategy Selection Services enabled');
    }

    // Risk Management Service
    if (this.config.enableRiskManagement) {
      const riskManagementService = new RiskManagementService();
      this.services.set('riskManagement', riskManagementService);
      logger.info('Risk Management Service enabled');
    }

    logger.info('Enhancement services initialized');
  }

  /**
   * Wire services together by setting up dependencies
   */
  private async wireServices(): Promise<void> {
    logger.info('Wiring services together...');

    const opportunityDetectionService = this.services.get('opportunityDetection');
    const executionService = this.services.get('execution');

    // Wire Pre-Execution Validation
    if (this.config.enablePreExecutionValidation) {
      const preExecutionValidationService = this.services.get('preExecutionValidation');
      const multiChainService = this.services.get('multiChain');
      
      // Set dependencies
      preExecutionValidationService.setMultiChainService(multiChainService);
      opportunityDetectionService.setPreExecutionValidationService(preExecutionValidationService);
      
      logger.info('Pre-Execution Validation wired');
    }

    // Wire MEV Protection
    if (this.config.enableMEVProtection) {
      const mevProtectionService = this.services.get('mevProtection');
      executionService.setMEVProtectionService(mevProtectionService);
      
      logger.info('MEV Protection wired');
    }

    // Wire ML Services
    if (this.config.enableMLLearning) {
      const mlLearningService = this.services.get('mlLearning');
      const strategySelectionService = this.services.get('strategySelection');
      
      executionService.setMLServices(mlLearningService, strategySelectionService);
      
      logger.info('ML Services wired');
    }

    // Wire Risk Management
    if (this.config.enableRiskManagement) {
      const riskManagementService = this.services.get('riskManagement');
      
      // Set up event listeners for risk monitoring
      executionService.on('trade', (trade) => {
        riskManagementService.evaluateTrade(trade);
      });
      
      executionService.on('tradeUpdate', (trade) => {
        riskManagementService.updateTradeHistory(trade);
      });
      
      logger.info('Risk Management wired');
    }

    // Wire opportunity flow
    opportunityDetectionService.on('opportunity', (opportunity) => {
      executionService.evaluateOpportunity(opportunity);
    });

    logger.info('Service wiring completed');
  }

  /**
   * Start all services in the correct order
   */
  private async startServices(): Promise<void> {
    logger.info('Starting services...');

    const startOrder = [
      'supabase',
      'multiChain',
      'priceFeed',
      'tokenDiscovery',
      'preExecutionValidation',
      'mevProtection',
      'mlLearning',
      'strategySelection',
      'riskManagement',
      'opportunityDetection',
      'execution'
    ];

    for (const serviceName of startOrder) {
      const service = this.services.get(serviceName);
      if (service && typeof service.start === 'function') {
        try {
          await service.start();
          logger.info(`${serviceName} service started`);
        } catch (error) {
          logger.error(`Failed to start ${serviceName} service:`, error);
          throw error;
        }
      }
    }

    logger.info('All services started successfully');
  }

  /**
   * Stop all services gracefully
   */
  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      logger.warn('ServiceIntegrator not initialized, nothing to shutdown');
      return;
    }

    logger.info('Shutting down services...');

    const stopOrder = [
      'execution',
      'opportunityDetection',
      'riskManagement',
      'strategySelection',
      'mlLearning',
      'mevProtection',
      'preExecutionValidation',
      'tokenDiscovery',
      'priceFeed',
      'multiChain',
      'supabase'
    ];

    for (const serviceName of stopOrder) {
      const service = this.services.get(serviceName);
      if (service && typeof service.stop === 'function') {
        try {
          await service.stop();
          logger.info(`${serviceName} service stopped`);
        } catch (error) {
          logger.error(`Error stopping ${serviceName} service:`, error);
        }
      }
    }

    this.isInitialized = false;
    logger.info('All services shut down');
  }

  /**
   * Get a specific service instance
   */
  public getService<T>(serviceName: string): T | undefined {
    return this.services.get(serviceName);
  }

  /**
   * Get all service instances
   */
  public getAllServices(): Map<string, any> {
    return new Map(this.services);
  }

  /**
   * Check if services are healthy
   */
  public async healthCheck(): Promise<{ [serviceName: string]: boolean }> {
    const healthStatus: { [serviceName: string]: boolean } = {};

    for (const [serviceName, service] of this.services) {
      try {
        if (typeof service.isHealthy === 'function') {
          healthStatus[serviceName] = service.isHealthy();
        } else {
          healthStatus[serviceName] = true; // Assume healthy if no health check method
        }
      } catch (error) {
        logger.error(`Health check failed for ${serviceName}:`, error);
        healthStatus[serviceName] = false;
      }
    }

    return healthStatus;
  }

  /**
   * Get integration statistics
   */
  public getIntegrationStats() {
    return {
      isInitialized: this.isInitialized,
      totalServices: this.services.size,
      enabledFeatures: {
        preExecutionValidation: this.config.enablePreExecutionValidation,
        mevProtection: this.config.enableMEVProtection,
        mlLearning: this.config.enableMLLearning,
        riskManagement: this.config.enableRiskManagement
      },
      services: Array.from(this.services.keys())
    };
  }
}
