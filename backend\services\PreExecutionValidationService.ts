import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';
import { MultiChainService } from './MultiChainService.js';

export interface ValidationResult {
  isValid: boolean;
  reason?: string;
  simulatedProfit: number;
  totalCosts: CostBreakdown;
  profitMargin: number;
  executionTime: number;
  riskScore: number;
}

export interface CostBreakdown {
  gasCosts: number;
  dexFees: number;
  bridgeFees: number;
  slippageCosts: number;
  mevProtectionCosts: number;
  networkCongestionPenalty: number;
  totalCosts: number;
}

export interface SimulationParameters {
  tradeSize: number;
  currentGasPrice: number;
  networkCongestion: number;
  liquidityDepth: number;
  volatility: number;
  bridgeUtilization?: number;
}

export class PreExecutionValidationService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private multiChainService: MultiChainService | null = null;
  private isRunning = false;
  
  // Validation parameters
  private readonly minProfitMarginBuffer = parseFloat(config.MIN_PROFIT_MARGIN_BUFFER);
  private readonly maxSimulationTime = parseInt(config.MAX_SIMULATION_TIME);
  private readonly slippageBuffer = parseFloat(config.SIMULATION_SLIPPAGE_BUFFER);
  private readonly enableBridgeFeeCalc = config.ENABLE_BRIDGE_FEE_CALCULATION === 'true';
  private readonly enableCongestionCheck = config.ENABLE_NETWORK_CONGESTION_CHECK === 'true';

  constructor() {
    super();
    this.initializeProviders();
  }

  public setMultiChainService(multiChainService: MultiChainService) {
    this.multiChainService = multiChainService;
    logger.info('MultiChainService integrated with PreExecutionValidationService');
  }

  private initializeProviders() {
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting Pre-Execution Validation Service...');
    this.isRunning = true;
    logger.info('Pre-Execution Validation Service started');
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping Pre-Execution Validation Service...');
    this.isRunning = false;
    logger.info('Pre-Execution Validation Service stopped');
  }

  /**
   * Main validation method - performs comprehensive pre-execution validation
   */
  public async validateOpportunity(opportunity: ArbitrageOpportunity): Promise<ValidationResult> {
    const startTime = Date.now();
    
    try {
      logger.debug(`Starting validation for opportunity ${opportunity.id}`);

      // Gather current market conditions
      const simulationParams = await this.gatherSimulationParameters(opportunity);
      
      // Perform comprehensive cost calculation
      const costBreakdown = await this.calculateComprehensiveCosts(opportunity, simulationParams);
      
      // Calculate actual profit after all costs
      const simulatedProfit = opportunity.potentialProfit - costBreakdown.totalCosts;
      
      // Calculate profit margin
      const profitMargin = (simulatedProfit / opportunity.potentialProfit) * 100;
      
      // Check minimum profit margin buffer
      const isValid = profitMargin >= this.minProfitMarginBuffer && simulatedProfit > 0;
      
      // Calculate risk score
      const riskScore = this.calculateRiskScore(opportunity, simulationParams, costBreakdown);
      
      const executionTime = Date.now() - startTime;
      
      const result: ValidationResult = {
        isValid,
        reason: isValid ? undefined : this.getValidationFailureReason(profitMargin, simulatedProfit),
        simulatedProfit,
        totalCosts: costBreakdown,
        profitMargin,
        executionTime,
        riskScore
      };

      // Log validation result
      this.logValidationResult(opportunity, result);
      
      // Emit validation event for ML learning
      this.emit('validationComplete', {
        opportunity,
        result,
        simulationParams
      });

      return result;

    } catch (error) {
      logger.error(`Validation failed for opportunity ${opportunity.id}:`, error);
      
      return {
        isValid: false,
        reason: `Validation error: ${error.message}`,
        simulatedProfit: 0,
        totalCosts: this.getEmptyCostBreakdown(),
        profitMargin: 0,
        executionTime: Date.now() - startTime,
        riskScore: 100 // Maximum risk for failed validation
      };
    }
  }

  /**
   * Gather current market conditions for simulation
   */
  private async gatherSimulationParameters(opportunity: ArbitrageOpportunity): Promise<SimulationParameters> {
    const provider = this.providers.get(opportunity.network);
    if (!provider) {
      throw new Error(`No provider configured for network: ${opportunity.network}`);
    }

    // Get current gas price
    const feeData = await provider.getFeeData();
    const currentGasPrice = Number(feeData.gasPrice || 0) / 1e9; // Convert to Gwei

    // Estimate network congestion (simplified - would use real metrics in production)
    const networkCongestion = await this.estimateNetworkCongestion(opportunity.network);
    
    // Calculate trade size based on opportunity
    const tradeSize = this.calculateOptimalTradeSize(opportunity);
    
    // Estimate liquidity depth
    const liquidityDepth = await this.estimateLiquidityDepth(opportunity);
    
    // Get market volatility
    const volatility = await this.getMarketVolatility(opportunity);

    // For cross-chain, get bridge utilization
    let bridgeUtilization: number | undefined;
    if (opportunity.type === ArbitrageType.CROSS_CHAIN && this.enableBridgeFeeCalc) {
      bridgeUtilization = await this.getBridgeUtilization(opportunity);
    }

    return {
      tradeSize,
      currentGasPrice,
      networkCongestion,
      liquidityDepth,
      volatility,
      bridgeUtilization
    };
  }

  /**
   * Calculate comprehensive costs including all fees and penalties
   */
  private async calculateComprehensiveCosts(
    opportunity: ArbitrageOpportunity, 
    params: SimulationParameters
  ): Promise<CostBreakdown> {
    
    // Calculate gas costs with current network conditions
    const gasCosts = await this.calculateGasCosts(opportunity, params);
    
    // Calculate DEX trading fees
    const dexFees = this.calculateDexFees(opportunity, params);
    
    // Calculate bridge fees for cross-chain opportunities
    const bridgeFees = await this.calculateBridgeFees(opportunity, params);
    
    // Calculate slippage costs with buffer
    const slippageCosts = this.calculateSlippageCosts(opportunity, params);
    
    // Calculate MEV protection costs
    const mevProtectionCosts = this.calculateMevProtectionCosts(opportunity, params);
    
    // Calculate network congestion penalty
    const networkCongestionPenalty = this.calculateCongestionPenalty(opportunity, params);
    
    const totalCosts = gasCosts + dexFees + bridgeFees + slippageCosts + 
                      mevProtectionCosts + networkCongestionPenalty;

    return {
      gasCosts,
      dexFees,
      bridgeFees,
      slippageCosts,
      mevProtectionCosts,
      networkCongestionPenalty,
      totalCosts
    };
  }

  /**
   * Calculate gas costs with dynamic pricing
   */
  private async calculateGasCosts(
    opportunity: ArbitrageOpportunity, 
    params: SimulationParameters
  ): Promise<number> {
    
    let baseGasUnits = opportunity.estimatedGas || 300000;
    
    // Adjust gas units based on arbitrage type
    switch (opportunity.type) {
      case ArbitrageType.CROSS_CHAIN:
        baseGasUnits *= 2.5; // Cross-chain requires more gas
        break;
      case ArbitrageType.TRIANGULAR:
        baseGasUnits *= 1.8; // Multiple swaps
        break;
    }
    
    // Apply network congestion multiplier
    const congestionMultiplier = this.enableCongestionCheck ? 
      1 + (params.networkCongestion / 100) * 0.5 : 1;
    
    const adjustedGasUnits = baseGasUnits * congestionMultiplier;
    
    // Calculate gas cost in USD (simplified conversion)
    const gasPrice = params.currentGasPrice * parseFloat(config.GAS_PRICE_MULTIPLIER);
    const gasCostInNative = (adjustedGasUnits * gasPrice) / 1e9;
    
    // Convert to USD (would use real price feeds in production)
    const nativeToUsdRate = await this.getNativeTokenPrice(opportunity.network);
    
    return gasCostInNative * nativeToUsdRate;
  }

  /**
   * Calculate DEX trading fees
   */
  private calculateDexFees(opportunity: ArbitrageOpportunity, params: SimulationParameters): number {
    // Standard DEX fees (0.3% for most Uniswap-like DEXes)
    const standardFeeRate = 0.003;
    
    // Calculate fees based on trade size and number of swaps
    let swapCount = 2; // Default for simple arbitrage
    
    if (opportunity.type === ArbitrageType.TRIANGULAR) {
      swapCount = 3;
    } else if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      swapCount = 2; // One on each chain
    }
    
    return params.tradeSize * standardFeeRate * swapCount;
  }

  /**
   * Calculate bridge fees for cross-chain opportunities
   */
  private async calculateBridgeFees(
    opportunity: ArbitrageOpportunity, 
    params: SimulationParameters
  ): Promise<number> {
    
    if (opportunity.type !== ArbitrageType.CROSS_CHAIN || !this.enableBridgeFeeCalc) {
      return 0;
    }
    
    // Base bridge fee (varies by bridge protocol)
    const baseBridgeFee = 10; // USD
    
    // Variable fee based on amount (typically 0.05-0.1%)
    const variableFeeRate = 0.001;
    const variableFee = params.tradeSize * variableFeeRate;
    
    // Congestion multiplier for bridge utilization
    const utilizationMultiplier = params.bridgeUtilization ? 
      1 + (params.bridgeUtilization / 100) * 0.3 : 1;
    
    return (baseBridgeFee + variableFee) * utilizationMultiplier;
  }

  /**
   * Calculate slippage costs with buffer
   */
  private calculateSlippageCosts(
    opportunity: ArbitrageOpportunity, 
    params: SimulationParameters
  ): number {
    
    // Base slippage from opportunity detection
    const baseSlippage = opportunity.slippage / 100;
    
    // Add buffer for market volatility
    const volatilityBuffer = (params.volatility / 100) * 0.1;
    
    // Add configured slippage buffer
    const totalSlippage = baseSlippage + volatilityBuffer + (this.slippageBuffer / 100);
    
    return params.tradeSize * totalSlippage;
  }

  /**
   * Calculate MEV protection costs
   */
  private calculateMevProtectionCosts(
    opportunity: ArbitrageOpportunity, 
    params: SimulationParameters
  ): number {
    
    if (config.ENABLE_MEV_PROTECTION !== 'true') {
      return 0;
    }
    
    // Flashbots typically takes a small percentage of MEV
    const mevProtectionFeeRate = 0.001; // 0.1%
    
    return opportunity.potentialProfit * mevProtectionFeeRate;
  }

  /**
   * Calculate network congestion penalty
   */
  private calculateCongestionPenalty(
    opportunity: ArbitrageOpportunity,
    params: SimulationParameters
  ): number {

    if (!this.enableCongestionCheck || params.networkCongestion < 70) {
      return 0;
    }

    // Penalty for high congestion (increased failure risk)
    const congestionPenalty = (params.networkCongestion - 70) / 100;

    return opportunity.potentialProfit * congestionPenalty * 0.05; // 5% penalty per 100% congestion
  }

  /**
   * Calculate risk score based on various factors
   */
  private calculateRiskScore(
    opportunity: ArbitrageOpportunity,
    params: SimulationParameters,
    costs: CostBreakdown
  ): number {
    let riskScore = 0;

    // Slippage risk (0-30 points)
    riskScore += Math.min(opportunity.slippage * 6, 30);

    // Network congestion risk (0-25 points)
    riskScore += Math.min(params.networkCongestion * 0.25, 25);

    // Volatility risk (0-20 points)
    riskScore += Math.min(params.volatility * 0.67, 20);

    // Cost ratio risk (0-15 points)
    const costRatio = costs.totalCosts / opportunity.potentialProfit;
    riskScore += Math.min(costRatio * 30, 15);

    // Cross-chain additional risk (0-10 points)
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      riskScore += 10;
    }

    return Math.min(Math.round(riskScore), 100);
  }

  /**
   * Helper methods for market data gathering
   */
  private async estimateNetworkCongestion(network: string): Promise<number> {
    try {
      const provider = this.providers.get(network);
      if (!provider) return 50; // Default moderate congestion

      // Get recent block and calculate congestion based on gas usage
      const latestBlock = await provider.getBlock('latest');
      if (!latestBlock) return 50;

      const gasUsedRatio = Number(latestBlock.gasUsed) / Number(latestBlock.gasLimit);
      return Math.min(gasUsedRatio * 100, 100);

    } catch (error) {
      logger.warn(`Failed to estimate network congestion for ${network}:`, error);
      return 50; // Default to moderate congestion
    }
  }

  private calculateOptimalTradeSize(opportunity: ArbitrageOpportunity): number {
    // Calculate based on available liquidity and max position size
    const maxPositionSize = parseFloat(config.MAX_POSITION_SIZE);
    const liquidityBasedSize = opportunity.potentialProfit * 20; // Rough estimate

    return Math.min(maxPositionSize, liquidityBasedSize, 50000); // Cap at $50k
  }

  private async estimateLiquidityDepth(opportunity: ArbitrageOpportunity): Promise<number> {
    // Simplified liquidity estimation
    // In production, this would query actual DEX liquidity
    return 1000000 + Math.random() * 5000000; // $1M - $6M range
  }

  private async getMarketVolatility(opportunity: ArbitrageOpportunity): Promise<number> {
    // Simplified volatility calculation
    // In production, this would use historical price data
    return 10 + Math.random() * 40; // 10-50% volatility range
  }

  private async getBridgeUtilization(opportunity: ArbitrageOpportunity): Promise<number> {
    // Simplified bridge utilization
    // In production, this would query actual bridge metrics
    return Math.random() * 100; // 0-100% utilization
  }

  private async getNativeTokenPrice(network: string): Promise<number> {
    // Simplified price conversion
    // In production, this would use real price feeds
    const prices: { [key: string]: number } = {
      'ethereum': 2000,
      'polygon': 0.8,
      'bsc': 300,
      'arbitrum': 2000,
      'optimism': 2000
    };

    return prices[network] || 1;
  }

  private getValidationFailureReason(profitMargin: number, simulatedProfit: number): string {
    if (simulatedProfit <= 0) {
      return 'Opportunity not profitable after all costs';
    }

    if (profitMargin < this.minProfitMarginBuffer) {
      return `Profit margin ${profitMargin.toFixed(2)}% below required buffer of ${this.minProfitMarginBuffer}%`;
    }

    return 'Unknown validation failure';
  }

  private getEmptyCostBreakdown(): CostBreakdown {
    return {
      gasCosts: 0,
      dexFees: 0,
      bridgeFees: 0,
      slippageCosts: 0,
      mevProtectionCosts: 0,
      networkCongestionPenalty: 0,
      totalCosts: 0
    };
  }

  private logValidationResult(opportunity: ArbitrageOpportunity, result: ValidationResult) {
    const logLevel = result.isValid ? 'info' : 'debug';

    logger[logLevel](`Validation ${result.isValid ? 'PASSED' : 'FAILED'} for opportunity ${opportunity.id}`, {
      type: opportunity.type,
      assets: opportunity.assets,
      originalProfit: opportunity.potentialProfit,
      simulatedProfit: result.simulatedProfit,
      profitMargin: result.profitMargin,
      totalCosts: result.totalCosts.totalCosts,
      riskScore: result.riskScore,
      executionTime: result.executionTime,
      reason: result.reason
    });
  }

  /**
   * Public method to get validation statistics
   */
  public getValidationStats() {
    return {
      isRunning: this.isRunning,
      minProfitMarginBuffer: this.minProfitMarginBuffer,
      maxSimulationTime: this.maxSimulationTime,
      enableBridgeFeeCalc: this.enableBridgeFeeCalc,
      enableCongestionCheck: this.enableCongestionCheck
    };
  }
}
