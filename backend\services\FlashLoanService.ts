import { EventEmitter } from 'events';
import { ethers } from 'ethers';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { ArbitrageOpportunity, ArbitrageType } from './OpportunityDetectionService.js';

export enum FlashLoanProvider {
  AAVE_V3 = 'aave_v3',
  BALANCER_V2 = 'balancer_v2',
  DYDX = 'dydx',
  UNISWAP_V3 = 'uniswap_v3',
  COMPOUND = 'compound'
}

export enum FlashLoanStrategy {
  FLASH_LOAN = 'flash_loan',
  FLASH_SWAP = 'flash_swap',
  HYBRID = 'hybrid'
}

export interface FlashLoanProviderInfo {
  provider: FlashLoanProvider;
  name: string;
  feeRate: number; // in basis points (e.g., 9 = 0.09%)
  maxLiquidity: number; // in USD
  gasOverhead: number; // additional gas cost
  supportedNetworks: string[];
  supportedAssets: string[];
  isActive: boolean;
  contractAddress: string;
  minAmount: number; // minimum flash loan amount
  maxAmount: number; // maximum flash loan amount
}

export interface FlashLoanQuote {
  provider: FlashLoanProvider;
  asset: string;
  amount: number;
  fee: number;
  feeRate: number;
  gasEstimate: number;
  totalCost: number;
  availableLiquidity: number;
  executionTime: number;
  isAvailable: boolean;
  reason?: string;
}

export interface FlashLoanExecution {
  provider: FlashLoanProvider;
  strategy: FlashLoanStrategy;
  asset: string;
  amount: number;
  fee: number;
  gasUsed: number;
  executionTime: number;
  success: boolean;
  transactionHash?: string;
  error?: string;
  profitAfterFees: number;
}

export interface FlashLoanOptimization {
  recommendedProvider: FlashLoanProvider;
  recommendedStrategy: FlashLoanStrategy;
  optimalAmount: number;
  totalCost: number;
  expectedProfit: number;
  riskScore: number;
  reasoning: string;
  alternatives: FlashLoanQuote[];
}

export class FlashLoanService extends EventEmitter {
  private providers: Map<string, ethers.JsonRpcProvider> = new Map();
  private flashLoanProviders: Map<FlashLoanProvider, FlashLoanProviderInfo> = new Map();
  private isRunning = false;
  
  // Flash loan parameters
  private readonly maxProviderCheckTime = 3000; // 3 seconds
  private readonly minProfitMargin = 0.1; // 10% minimum profit margin
  private readonly maxGasPrice = 100; // 100 Gwei max
  
  constructor() {
    super();
    this.initializeProviders();
    this.initializeFlashLoanProviders();
  }

  private initializeProviders() {
    this.providers.set('ethereum', new ethers.JsonRpcProvider(config.ETHEREUM_RPC_URL));
    this.providers.set('polygon', new ethers.JsonRpcProvider(config.POLYGON_RPC_URL));
    this.providers.set('bsc', new ethers.JsonRpcProvider(config.BSC_RPC_URL));
  }

  private initializeFlashLoanProviders() {
    // Aave V3 - Primary provider with competitive rates
    this.flashLoanProviders.set(FlashLoanProvider.AAVE_V3, {
      provider: FlashLoanProvider.AAVE_V3,
      name: 'Aave V3',
      feeRate: 5, // 0.05%
      maxLiquidity: 100000000, // $100M
      gasOverhead: 150000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum', 'optimism', 'avalanche'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC'],
      isActive: true,
      contractAddress: '******************************************', // Ethereum mainnet
      minAmount: 1, // $1
      maxAmount: 50000000 // $50M
    });

    // Balancer V2 - Alternative with vault-based flash loans
    this.flashLoanProviders.set(FlashLoanProvider.BALANCER_V2, {
      provider: FlashLoanProvider.BALANCER_V2,
      name: 'Balancer V2',
      feeRate: 0, // 0% fee but requires gas
      maxLiquidity: 50000000, // $50M
      gasOverhead: 120000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'BAL'],
      isActive: true,
      contractAddress: '******************************************', // Vault address
      minAmount: 1,
      maxAmount: 25000000
    });

    // dYdX - Zero-fee flash loans (where available)
    this.flashLoanProviders.set(FlashLoanProvider.DYDX, {
      provider: FlashLoanProvider.DYDX,
      name: 'dYdX',
      feeRate: 0, // 0% fee
      maxLiquidity: 20000000, // $20M
      gasOverhead: 100000,
      supportedNetworks: ['ethereum'],
      supportedAssets: ['USDC', 'WETH', 'DAI'],
      isActive: true,
      contractAddress: '******************************************', // Solo Margin
      minAmount: 1,
      maxAmount: 10000000
    });

    // Uniswap V3 - Flash swaps for token pairs
    this.flashLoanProviders.set(FlashLoanProvider.UNISWAP_V3, {
      provider: FlashLoanProvider.UNISWAP_V3,
      name: 'Uniswap V3',
      feeRate: 30, // 0.3% (pool fee)
      maxLiquidity: 200000000, // $200M
      gasOverhead: 80000,
      supportedNetworks: ['ethereum', 'polygon', 'arbitrum', 'optimism', 'base'],
      supportedAssets: ['USDC', 'USDT', 'DAI', 'WETH', 'WBTC', 'UNI'],
      isActive: true,
      contractAddress: '******************************************', // SwapRouter
      minAmount: 1,
      maxAmount: 100000000
    });
  }

  public async start() {
    if (this.isRunning) return;
    
    logger.info('Starting Flash Loan Service...');
    this.isRunning = true;
    
    // Validate provider connections
    await this.validateProviderConnections();
    
    logger.info('Flash Loan Service started');
  }

  public async stop() {
    if (!this.isRunning) return;
    
    logger.info('Stopping Flash Loan Service...');
    this.isRunning = false;
    logger.info('Flash Loan Service stopped');
  }

  /**
   * Automatically determine optimal flash loan strategy for an arbitrage opportunity
   */
  public async optimizeFlashLoanStrategy(
    opportunity: ArbitrageOpportunity,
    availableCapital: number = 0
  ): Promise<FlashLoanOptimization> {
    
    try {
      logger.debug(`Optimizing flash loan strategy for opportunity ${opportunity.id}`);

      // Determine if flash loan is needed
      const requiredCapital = this.calculateRequiredCapital(opportunity);
      const needsFlashLoan = requiredCapital > availableCapital;

      if (!needsFlashLoan) {
        return {
          recommendedProvider: FlashLoanProvider.AAVE_V3, // Default
          recommendedStrategy: FlashLoanStrategy.FLASH_LOAN,
          optimalAmount: 0,
          totalCost: 0,
          expectedProfit: opportunity.potentialProfit,
          riskScore: 10, // Low risk without flash loan
          reasoning: 'Sufficient capital available, no flash loan needed',
          alternatives: []
        };
      }

      // Get quotes from all available providers
      const quotes = await this.getFlashLoanQuotes(
        opportunity.assets[0], // Primary asset
        requiredCapital,
        opportunity.network
      );

      // Filter available quotes
      const availableQuotes = quotes.filter(quote => quote.isAvailable);

      if (availableQuotes.length === 0) {
        throw new Error('No flash loan providers available for this opportunity');
      }

      // Determine optimal strategy
      const strategy = this.determineOptimalStrategy(opportunity, availableQuotes);
      
      // Select best provider based on total cost and risk
      const bestQuote = this.selectOptimalProvider(availableQuotes, opportunity);

      const optimization: FlashLoanOptimization = {
        recommendedProvider: bestQuote.provider,
        recommendedStrategy: strategy,
        optimalAmount: bestQuote.amount,
        totalCost: bestQuote.totalCost,
        expectedProfit: opportunity.potentialProfit - bestQuote.totalCost,
        riskScore: this.calculateFlashLoanRiskScore(bestQuote, opportunity),
        reasoning: this.generateOptimizationReasoning(bestQuote, strategy, availableQuotes),
        alternatives: availableQuotes.filter(q => q.provider !== bestQuote.provider).slice(0, 3)
      };

      logger.info(`Flash loan optimization completed for ${opportunity.id}: ${optimization.recommendedProvider} (${optimization.recommendedStrategy})`);
      
      return optimization;

    } catch (error) {
      logger.error(`Flash loan optimization failed for opportunity ${opportunity.id}:`, error);
      throw error;
    }
  }

  /**
   * Get flash loan quotes from multiple providers
   */
  public async getFlashLoanQuotes(
    asset: string,
    amount: number,
    network: string
  ): Promise<FlashLoanQuote[]> {
    
    const quotes: FlashLoanQuote[] = [];
    const startTime = Date.now();

    // Get quotes from all providers in parallel
    const quotePromises = Array.from(this.flashLoanProviders.values())
      .filter(provider => 
        provider.isActive && 
        provider.supportedNetworks.includes(network) &&
        provider.supportedAssets.includes(asset)
      )
      .map(provider => this.getProviderQuote(provider, asset, amount, network));

    try {
      const results = await Promise.allSettled(quotePromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          quotes.push(result.value);
        } else {
          logger.warn(`Flash loan quote failed for provider:`, result.reason);
        }
      });

      const executionTime = Date.now() - startTime;
      logger.debug(`Retrieved ${quotes.length} flash loan quotes in ${executionTime}ms`);

      return quotes.sort((a, b) => a.totalCost - b.totalCost); // Sort by total cost

    } catch (error) {
      logger.error('Error getting flash loan quotes:', error);
      return [];
    }
  }

  /**
   * Get quote from a specific provider
   */
  private async getProviderQuote(
    provider: FlashLoanProviderInfo,
    asset: string,
    amount: number,
    network: string
  ): Promise<FlashLoanQuote> {
    
    const startTime = Date.now();

    try {
      // Check amount limits
      if (amount < provider.minAmount || amount > provider.maxAmount) {
        return {
          provider: provider.provider,
          asset,
          amount,
          fee: 0,
          feeRate: provider.feeRate,
          gasEstimate: 0,
          totalCost: Infinity,
          availableLiquidity: 0,
          executionTime: Date.now() - startTime,
          isAvailable: false,
          reason: `Amount ${amount} outside limits [${provider.minAmount}, ${provider.maxAmount}]`
        };
      }

      // Get available liquidity
      const availableLiquidity = await this.getProviderLiquidity(provider, asset, network);
      
      if (availableLiquidity < amount) {
        return {
          provider: provider.provider,
          asset,
          amount,
          fee: 0,
          feeRate: provider.feeRate,
          gasEstimate: 0,
          totalCost: Infinity,
          availableLiquidity,
          executionTime: Date.now() - startTime,
          isAvailable: false,
          reason: `Insufficient liquidity: ${availableLiquidity} < ${amount}`
        };
      }

      // Calculate fees and costs
      const fee = (amount * provider.feeRate) / 10000; // Convert basis points to decimal
      const gasEstimate = provider.gasOverhead;
      const gasCost = await this.estimateGasCost(gasEstimate, network);
      const totalCost = fee + gasCost;

      return {
        provider: provider.provider,
        asset,
        amount,
        fee,
        feeRate: provider.feeRate,
        gasEstimate,
        totalCost,
        availableLiquidity,
        executionTime: Date.now() - startTime,
        isAvailable: true
      };

    } catch (error) {
      logger.error(`Error getting quote from ${provider.name}:`, error);
      
      return {
        provider: provider.provider,
        asset,
        amount,
        fee: 0,
        feeRate: provider.feeRate,
        gasEstimate: 0,
        totalCost: Infinity,
        availableLiquidity: 0,
        executionTime: Date.now() - startTime,
        isAvailable: false,
        reason: `Provider error: ${error.message}`
      };
    }
  }

  /**
   * Calculate required capital for arbitrage opportunity
   */
  private calculateRequiredCapital(opportunity: ArbitrageOpportunity): number {
    // For intra-chain arbitrage, need capital for initial purchase
    if (opportunity.type === ArbitrageType.INTRA_CHAIN) {
      return opportunity.potentialProfit * 10; // Rough estimate: 10x profit as capital
    }

    // For cross-chain arbitrage, need capital for both sides
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      return opportunity.potentialProfit * 15; // Higher capital for cross-chain
    }

    // For triangular arbitrage, need capital for initial swap
    if (opportunity.type === ArbitrageType.TRIANGULAR) {
      return opportunity.potentialProfit * 12; // Medium capital requirement
    }

    return opportunity.potentialProfit * 10; // Default
  }

  /**
   * Determine optimal strategy based on opportunity characteristics
   */
  private determineOptimalStrategy(
    opportunity: ArbitrageOpportunity,
    quotes: FlashLoanQuote[]
  ): FlashLoanStrategy {

    // For token-to-token arbitrage with Uniswap, prefer flash swaps
    if (opportunity.type === ArbitrageType.INTRA_CHAIN &&
        opportunity.exchanges.some(ex => ex.toLowerCase().includes('uniswap'))) {
      return FlashLoanStrategy.FLASH_SWAP;
    }

    // For cross-chain arbitrage, use traditional flash loans
    if (opportunity.type === ArbitrageType.CROSS_CHAIN) {
      return FlashLoanStrategy.FLASH_LOAN;
    }

    // For triangular arbitrage, consider hybrid approach
    if (opportunity.type === ArbitrageType.TRIANGULAR) {
      return FlashLoanStrategy.HYBRID;
    }

    // Default to flash loan
    return FlashLoanStrategy.FLASH_LOAN;
  }

  /**
   * Select optimal provider based on cost and risk
   */
  private selectOptimalProvider(
    quotes: FlashLoanQuote[],
    opportunity: ArbitrageOpportunity
  ): FlashLoanQuote {

    if (quotes.length === 0) {
      throw new Error('No available flash loan quotes');
    }

    // Score each quote based on multiple factors
    const scoredQuotes = quotes.map(quote => {
      let score = 0;

      // Cost factor (lower cost = higher score)
      const costScore = Math.max(0, 100 - (quote.totalCost / opportunity.potentialProfit) * 100);
      score += costScore * 0.4;

      // Liquidity factor (higher liquidity = higher score)
      const liquidityScore = Math.min(100, (quote.availableLiquidity / quote.amount) * 10);
      score += liquidityScore * 0.3;

      // Speed factor (lower execution time = higher score)
      const speedScore = Math.max(0, 100 - quote.executionTime / 100);
      score += speedScore * 0.2;

      // Provider reliability factor
      const reliabilityScore = this.getProviderReliabilityScore(quote.provider);
      score += reliabilityScore * 0.1;

      return { quote, score };
    });

    // Sort by score and return best quote
    scoredQuotes.sort((a, b) => b.score - a.score);
    return scoredQuotes[0].quote;
  }

  /**
   * Calculate flash loan risk score
   */
  private calculateFlashLoanRiskScore(
    quote: FlashLoanQuote,
    opportunity: ArbitrageOpportunity
  ): number {
    let riskScore = 0;

    // Cost ratio risk (higher cost ratio = higher risk)
    const costRatio = quote.totalCost / opportunity.potentialProfit;
    riskScore += Math.min(costRatio * 50, 30);

    // Liquidity risk (lower liquidity buffer = higher risk)
    const liquidityBuffer = quote.availableLiquidity / quote.amount;
    if (liquidityBuffer < 2) riskScore += 20;
    else if (liquidityBuffer < 5) riskScore += 10;

    // Provider risk
    const providerRisk = this.getProviderRiskScore(quote.provider);
    riskScore += providerRisk;

    // Network congestion risk
    if (opportunity.network === 'ethereum') riskScore += 10;

    return Math.min(Math.round(riskScore), 100);
  }

  /**
   * Generate optimization reasoning
   */
  private generateOptimizationReasoning(
    bestQuote: FlashLoanQuote,
    strategy: FlashLoanStrategy,
    alternatives: FlashLoanQuote[]
  ): string {
    const provider = this.flashLoanProviders.get(bestQuote.provider);
    const savings = alternatives.length > 0 ?
      Math.min(...alternatives.map(q => q.totalCost)) - bestQuote.totalCost : 0;

    let reasoning = `Selected ${provider?.name} for ${strategy} strategy. `;
    reasoning += `Total cost: $${bestQuote.totalCost.toFixed(2)} `;
    reasoning += `(${bestQuote.feeRate / 100}% fee + gas). `;

    if (savings > 0) {
      reasoning += `Saves $${savings.toFixed(2)} vs alternatives. `;
    }

    reasoning += `Liquidity: $${bestQuote.availableLiquidity.toLocaleString()}.`;

    return reasoning;
  }

  /**
   * Get provider liquidity for specific asset
   */
  private async getProviderLiquidity(
    provider: FlashLoanProviderInfo,
    asset: string,
    network: string
  ): Promise<number> {

    try {
      // In production, would query actual contract liquidity
      // For demo, return simulated liquidity based on provider limits

      const baseMultiplier = Math.random() * 0.5 + 0.5; // 50-100% of max
      return provider.maxLiquidity * baseMultiplier;

    } catch (error) {
      logger.error(`Error getting liquidity for ${provider.name}:`, error);
      return 0;
    }
  }

  /**
   * Estimate gas cost for transaction
   */
  private async estimateGasCost(gasUnits: number, network: string): Promise<number> {
    try {
      const provider = this.providers.get(network);
      if (!provider) return gasUnits * 20 * 1e-9 * 2000; // Default estimate

      const feeData = await provider.getFeeData();
      const gasPrice = Number(feeData.gasPrice || 0) / 1e9; // Convert to Gwei

      // Convert to USD (simplified)
      const nativeToUsd = network === 'ethereum' ? 2000 :
                         network === 'polygon' ? 0.8 : 300;

      return (gasUnits * gasPrice * nativeToUsd) / 1e9;

    } catch (error) {
      logger.error(`Error estimating gas cost for ${network}:`, error);
      return gasUnits * 20 * 1e-9 * 2000; // Fallback estimate
    }
  }

  /**
   * Get provider reliability score
   */
  private getProviderReliabilityScore(provider: FlashLoanProvider): number {
    const scores: { [key in FlashLoanProvider]: number } = {
      [FlashLoanProvider.AAVE_V3]: 95,
      [FlashLoanProvider.BALANCER_V2]: 90,
      [FlashLoanProvider.DYDX]: 85,
      [FlashLoanProvider.UNISWAP_V3]: 88,
      [FlashLoanProvider.COMPOUND]: 82
    };

    return scores[provider] || 70;
  }

  /**
   * Get provider risk score
   */
  private getProviderRiskScore(provider: FlashLoanProvider): number {
    const riskScores: { [key in FlashLoanProvider]: number } = {
      [FlashLoanProvider.AAVE_V3]: 5,
      [FlashLoanProvider.BALANCER_V2]: 8,
      [FlashLoanProvider.DYDX]: 10,
      [FlashLoanProvider.UNISWAP_V3]: 7,
      [FlashLoanProvider.COMPOUND]: 12
    };

    return riskScores[provider] || 15;
  }

  /**
   * Validate provider connections
   */
  private async validateProviderConnections(): Promise<void> {
    logger.info('Validating flash loan provider connections...');

    let validProviders = 0;
    const totalProviders = this.flashLoanProviders.size;

    for (const [providerType, provider] of this.flashLoanProviders) {
      try {
        // In production, would check actual contract availability
        // For demo, simulate validation
        await new Promise(resolve => setTimeout(resolve, 100));

        logger.debug(`✅ ${provider.name} connection validated`);
        validProviders++;

      } catch (error) {
        logger.warn(`❌ ${provider.name} connection failed:`, error);
        provider.isActive = false;
      }
    }

    logger.info(`Flash loan providers validated: ${validProviders}/${totalProviders} active`);
  }

  /**
   * Get flash loan service statistics
   */
  public getFlashLoanStats() {
    const activeProviders = Array.from(this.flashLoanProviders.values())
      .filter(p => p.isActive);

    return {
      isRunning: this.isRunning,
      totalProviders: this.flashLoanProviders.size,
      activeProviders: activeProviders.length,
      supportedNetworks: [...new Set(activeProviders.flatMap(p => p.supportedNetworks))],
      supportedAssets: [...new Set(activeProviders.flatMap(p => p.supportedAssets))],
      providers: activeProviders.map(p => ({
        name: p.name,
        feeRate: p.feeRate,
        maxLiquidity: p.maxLiquidity,
        networks: p.supportedNetworks
      }))
    };
  }

  /**
   * Get provider information
   */
  public getProviderInfo(provider: FlashLoanProvider): FlashLoanProviderInfo | undefined {
    return this.flashLoanProviders.get(provider);
  }

  /**
   * Check if flash loan is available for opportunity
   */
  public async isFlashLoanAvailable(
    opportunity: ArbitrageOpportunity,
    requiredAmount: number
  ): Promise<boolean> {

    try {
      const quotes = await this.getFlashLoanQuotes(
        opportunity.assets[0],
        requiredAmount,
        opportunity.network
      );

      return quotes.some(quote => quote.isAvailable);

    } catch (error) {
      logger.error('Error checking flash loan availability:', error);
      return false;
    }
  }
}
