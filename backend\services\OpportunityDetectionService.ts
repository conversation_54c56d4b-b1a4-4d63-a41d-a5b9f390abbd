import { EventEmitter } from 'events';
import logger from '../utils/logger.js';
import config from '../config/index.js';
import { PriceFeedService, PriceData } from './PriceFeedService.js';
import { TokenDiscoveryService, Token } from './TokenDiscoveryService.js';
import { PreExecutionValidationService, ValidationResult } from './PreExecutionValidationService.js';

export enum ArbitrageType {
  INTRA_CHAIN = 'intra-chain',
  CROSS_CHAIN = 'cross-chain',
  TRIANGULAR = 'triangular'
}

export interface ArbitrageOpportunity {
  id: string;
  type: ArbitrageType;
  assets: string[];
  exchanges: string[];
  potentialProfit: number;
  profitPercentage: number;
  timestamp: number;
  network: string;
  route: TradingRoute;
  estimatedGas: number;
  slippage: number;
  confidence: number; // 0-100
  validationResult?: ValidationResult; // Added for pre-execution validation
  isValidated?: boolean; // Flag to indicate if validation was performed
}

export interface TradingRoute {
  steps: TradeStep[];
  totalGasCost: number;
  expectedProfit: number;
  priceImpact: number;
}

export interface TradeStep {
  exchange: string;
  tokenIn: string;
  tokenOut: string;
  amountIn: number;
  amountOut: number;
  priceImpact: number;
  gasCost: number;
}

export class OpportunityDetectionService extends EventEmitter {
  private priceFeedService: PriceFeedService;
  private tokenDiscoveryService: TokenDiscoveryService;
  private preExecutionValidationService: PreExecutionValidationService | null = null;
  private detectionInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private opportunities: Map<string, ArbitrageOpportunity> = new Map();

  // Configuration
  private minProfitThreshold = parseFloat(config.MIN_PROFIT_THRESHOLD);
  private maxSlippage = parseFloat(config.MAX_SLIPPAGE);
  private maxPositionSize = parseFloat(config.MAX_POSITION_SIZE);
  private enableMandatorySimulation = config.ENABLE_MANDATORY_SIMULATION === 'true';

  constructor(priceFeedService: PriceFeedService, tokenDiscoveryService: TokenDiscoveryService) {
    super();
    this.priceFeedService = priceFeedService;
    this.tokenDiscoveryService = tokenDiscoveryService;

    // Listen for price updates
    this.priceFeedService.on('priceUpdate', (priceData: PriceData) => {
      this.onPriceUpdate(priceData);
    });
  }

  public setPreExecutionValidationService(validationService: PreExecutionValidationService) {
    this.preExecutionValidationService = validationService;
    logger.info('PreExecutionValidationService integrated with OpportunityDetectionService');
  }

  public async start() {
    if (this.isRunning) return;

    logger.info('Starting Opportunity Detection Service...');
    this.isRunning = true;

    // Start detection loop
    this.detectionInterval = setInterval(() => {
      this.detectOpportunities();
    }, 1000); // Check every second

    // Initial detection
    await this.detectOpportunities();

    logger.info('Opportunity Detection Service started');
  }

  public async stop() {
    if (!this.isRunning) return;

    logger.info('Stopping Opportunity Detection Service...');
    this.isRunning = false;

    if (this.detectionInterval) {
      clearInterval(this.detectionInterval);
      this.detectionInterval = null;
    }

    this.opportunities.clear();
    logger.info('Opportunity Detection Service stopped');
  }

  private async onPriceUpdate(priceData: PriceData) {
    // Quick opportunity check on price updates
    if (this.isRunning) {
      await this.quickOpportunityCheck(priceData);
    }
  }

  private async quickOpportunityCheck(priceData: PriceData) {
    try {
      // Check for immediate arbitrage opportunities based on price update
      const tokens = this.tokenDiscoveryService.getWhitelistedTokens();
      const relevantTokens = tokens.filter(token => 
        token.symbol === priceData.symbol || 
        priceData.symbol.includes(token.symbol)
      );

      for (const token of relevantTokens) {
        await this.checkIntraChainArbitrage(token, priceData);
      }

    } catch (error) {
      logger.error('Error in quick opportunity check:', error);
    }
  }

  private async detectOpportunities() {
    try {
      logger.debug('Detecting arbitrage opportunities...');

      const tokens = this.tokenDiscoveryService.getWhitelistedTokens();
      const validTokens = tokens.filter(token => 
        token.liquidity >= 2000 && // Minimum liquidity threshold
        token.safetyScore >= 70
      );

      // Detect different types of arbitrage
      await Promise.all([
        this.detectIntraChainOpportunities(validTokens),
        this.detectCrossChainOpportunities(validTokens),
        this.detectTriangularOpportunities(validTokens)
      ]);

      // Clean up old opportunities
      this.cleanupOldOpportunities();

    } catch (error) {
      logger.error('Error detecting opportunities:', error);
    }
  }

  private async detectIntraChainOpportunities(tokens: Token[]) {
    try {
      for (const token of tokens) {
        const prices = this.priceFeedService.getPricesBySource('dex')
          .filter(p => p.symbol.includes(token.symbol));

        if (prices.length >= 2) {
          await this.analyzeIntraChainArbitrage(token, prices);
        }
      }
    } catch (error) {
      logger.error('Error detecting intra-chain opportunities:', error);
    }
  }

  private async analyzeIntraChainArbitrage(token: Token, prices: PriceData[]) {
    try {
      // Find price differences between exchanges
      for (let i = 0; i < prices.length; i++) {
        for (let j = i + 1; j < prices.length; j++) {
          const price1 = prices[i];
          const price2 = prices[j];
          
          const priceDiff = Math.abs(price1.price - price2.price);
          const avgPrice = (price1.price + price2.price) / 2;
          const profitPercentage = (priceDiff / avgPrice) * 100;

          if (profitPercentage > 0.5) { // Minimum 0.5% profit
            const opportunity = await this.createIntraChainOpportunity(
              token, price1, price2, profitPercentage
            );
            
            if (opportunity && opportunity.potentialProfit >= this.minProfitThreshold) {
              await this.addOpportunity(opportunity);
            }
          }
        }
      }
    } catch (error) {
      logger.error('Error analyzing intra-chain arbitrage:', error);
    }
  }

  private async createIntraChainOpportunity(
    token: Token, 
    price1: PriceData, 
    price2: PriceData, 
    profitPercentage: number
  ): Promise<ArbitrageOpportunity | null> {
    try {
      const buyExchange = price1.price < price2.price ? price1.source : price2.source;
      const sellExchange = price1.price < price2.price ? price2.source : price1.source;
      const buyPrice = Math.min(price1.price, price2.price);
      const sellPrice = Math.max(price1.price, price2.price);

      // Calculate trade size based on liquidity
      const maxTradeSize = Math.min(
        this.maxPositionSize,
        token.liquidity * 0.1 // Max 10% of liquidity
      );

      const tradeSize = maxTradeSize / buyPrice;
      const grossProfit = tradeSize * (sellPrice - buyPrice);
      
      // Estimate costs
      const estimatedGas = 150000; // Typical gas for DEX swap
      const gasCost = estimatedGas * 20 * 1e-9 * 2000; // 20 gwei * ETH price
      const slippage = this.estimateSlippage(tradeSize, token.liquidity);
      const slippageCost = grossProfit * (slippage / 100);
      
      const netProfit = grossProfit - gasCost - slippageCost;

      if (netProfit <= 0) return null;

      const route: TradingRoute = {
        steps: [
          {
            exchange: buyExchange,
            tokenIn: 'USDC',
            tokenOut: token.symbol,
            amountIn: tradeSize * buyPrice,
            amountOut: tradeSize,
            priceImpact: slippage / 2,
            gasCost: gasCost / 2
          },
          {
            exchange: sellExchange,
            tokenIn: token.symbol,
            tokenOut: 'USDC',
            amountIn: tradeSize,
            amountOut: tradeSize * sellPrice,
            priceImpact: slippage / 2,
            gasCost: gasCost / 2
          }
        ],
        totalGasCost: gasCost,
        expectedProfit: netProfit,
        priceImpact: slippage
      };

      return {
        id: `intra_${token.symbol}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: ArbitrageType.INTRA_CHAIN,
        assets: [token.symbol, 'USDC'],
        exchanges: [buyExchange, sellExchange],
        potentialProfit: netProfit,
        profitPercentage,
        timestamp: Date.now(),
        network: token.network,
        route,
        estimatedGas,
        slippage,
        confidence: this.calculateConfidence(token, profitPercentage, slippage)
      };

    } catch (error) {
      logger.error('Error creating intra-chain opportunity:', error);
      return null;
    }
  }

  private async checkIntraChainArbitrage(token: Token, priceData: PriceData) {
    try {
      // Quick check for arbitrage opportunities
      const otherPrices = this.priceFeedService.getAllPrices()
        .filter(p => 
          p.symbol === priceData.symbol && 
          p.source !== priceData.source &&
          Math.abs(p.timestamp - priceData.timestamp) < 10000 // Within 10 seconds
        );

      for (const otherPrice of otherPrices) {
        const priceDiff = Math.abs(priceData.price - otherPrice.price);
        const avgPrice = (priceData.price + otherPrice.price) / 2;
        const profitPercentage = (priceDiff / avgPrice) * 100;

        if (profitPercentage > 0.5) {
          const opportunity = await this.createIntraChainOpportunity(
            token, priceData, otherPrice, profitPercentage
          );
          
          if (opportunity && opportunity.potentialProfit >= this.minProfitThreshold) {
            await this.addOpportunity(opportunity);
          }
        }
      }
    } catch (error) {
      logger.error('Error checking intra-chain arbitrage:', error);
    }
  }

  private async detectCrossChainOpportunities(tokens: Token[]) {
    try {
      // Group tokens by symbol across different networks
      const tokensBySymbol: Map<string, Token[]> = new Map();
      
      tokens.forEach(token => {
        if (!tokensBySymbol.has(token.symbol)) {
          tokensBySymbol.set(token.symbol, []);
        }
        tokensBySymbol.get(token.symbol)!.push(token);
      });

      // Check for cross-chain arbitrage
      for (const [symbol, tokenList] of tokensBySymbol) {
        if (tokenList.length > 1) {
          await this.analyzeCrossChainArbitrage(symbol, tokenList);
        }
      }
    } catch (error) {
      logger.error('Error detecting cross-chain opportunities:', error);
    }
  }

  private async analyzeCrossChainArbitrage(symbol: string, tokens: Token[]) {
    try {
      // This is a simplified implementation
      // In production, you would need to account for bridge costs and time
      
      for (let i = 0; i < tokens.length; i++) {
        for (let j = i + 1; j < tokens.length; j++) {
          const token1 = tokens[i];
          const token2 = tokens[j];
          
          const price1 = await this.priceFeedService.getPrice(symbol, 'aggregated');
          const price2 = await this.priceFeedService.getPrice(symbol, 'aggregated');
          
          if (price1 && price2) {
            // Simulate cross-chain opportunity detection
            // This would involve complex bridge cost calculations
          }
        }
      }
    } catch (error) {
      logger.error('Error analyzing cross-chain arbitrage:', error);
    }
  }

  private async detectTriangularOpportunities(tokens: Token[]) {
    try {
      // Find triangular arbitrage opportunities
      const majorTokens = tokens.filter(token => 
        ['ETH', 'WBTC', 'USDC', 'DAI', 'UNI'].includes(token.symbol)
      );

      for (let i = 0; i < majorTokens.length; i++) {
        for (let j = i + 1; j < majorTokens.length; j++) {
          for (let k = j + 1; k < majorTokens.length; k++) {
            await this.analyzeTriangularArbitrage([
              majorTokens[i], majorTokens[j], majorTokens[k]
            ]);
          }
        }
      }
    } catch (error) {
      logger.error('Error detecting triangular opportunities:', error);
    }
  }

  private async analyzeTriangularArbitrage(tokens: Token[]) {
    try {
      // This is a simplified implementation
      // In production, you would calculate the actual triangular arbitrage profit
      
      const [tokenA, tokenB, tokenC] = tokens;
      
      // Get prices for all pairs
      const priceAB = await this.priceFeedService.getPrice(`${tokenA.symbol}/${tokenB.symbol}`);
      const priceBC = await this.priceFeedService.getPrice(`${tokenB.symbol}/${tokenC.symbol}`);
      const priceCA = await this.priceFeedService.getPrice(`${tokenC.symbol}/${tokenA.symbol}`);
      
      if (priceAB && priceBC && priceCA) {
        // Calculate triangular arbitrage profit
        // This would involve complex calculations
      }
    } catch (error) {
      logger.error('Error analyzing triangular arbitrage:', error);
    }
  }

  private estimateSlippage(tradeSize: number, liquidity: number): number {
    // Simple slippage estimation based on trade size vs liquidity
    const liquidityRatio = tradeSize / liquidity;
    return Math.min(liquidityRatio * 100, this.maxSlippage);
  }

  private calculateConfidence(token: Token, profitPercentage: number, slippage: number): number {
    let confidence = 50; // Base confidence
    
    // Adjust based on token safety score
    confidence += (token.safetyScore - 70) * 0.5;
    
    // Adjust based on profit percentage
    confidence += Math.min(profitPercentage * 10, 30);
    
    // Adjust based on slippage
    confidence -= slippage * 5;
    
    // Adjust based on liquidity
    if (token.liquidity > 100000) confidence += 10;
    else if (token.liquidity < 10000) confidence -= 20;
    
    return Math.max(0, Math.min(100, confidence));
  }

  private async addOpportunity(opportunity: ArbitrageOpportunity) {
    // Perform mandatory simulation if enabled
    if (this.enableMandatorySimulation && this.preExecutionValidationService) {
      try {
        logger.debug(`Performing mandatory validation for opportunity ${opportunity.id}`);

        const validationResult = await this.preExecutionValidationService.validateOpportunity(opportunity);

        // Add validation result to opportunity
        opportunity.validationResult = validationResult;
        opportunity.isValidated = true;

        // Only add opportunity if validation passes
        if (!validationResult.isValid) {
          logger.debug(`Opportunity ${opportunity.id} failed validation: ${validationResult.reason}`);

          // Emit failed validation event for ML learning
          this.emit('validationFailed', {
            opportunity,
            validationResult,
            timestamp: Date.now()
          });

          return; // Don't add invalid opportunities
        }

        // Update opportunity with validated profit
        opportunity.potentialProfit = validationResult.simulatedProfit;

        logger.info(`Opportunity ${opportunity.id} passed validation - Simulated profit: $${validationResult.simulatedProfit.toFixed(2)} (${validationResult.profitMargin.toFixed(2)}% margin)`);

      } catch (error) {
        logger.error(`Validation failed for opportunity ${opportunity.id}:`, error);

        // If validation fails due to error, don't add the opportunity
        this.emit('validationError', {
          opportunity,
          error: error.message,
          timestamp: Date.now()
        });

        return;
      }
    } else {
      // Mark as not validated if simulation is disabled
      opportunity.isValidated = false;
    }

    // Add validated opportunity
    this.opportunities.set(opportunity.id, opportunity);
    this.emit('opportunity', opportunity);

    const validationStatus = opportunity.isValidated ? 'validated' : 'unvalidated';
    logger.info(`New ${validationStatus} ${opportunity.type} opportunity: ${opportunity.assets.join('/')} - $${opportunity.potentialProfit.toFixed(2)} profit`);
  }

  private cleanupOldOpportunities() {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds
    
    for (const [id, opportunity] of this.opportunities) {
      if (now - opportunity.timestamp > maxAge) {
        this.opportunities.delete(id);
      }
    }
  }

  public getOpportunities(): ArbitrageOpportunity[] {
    return Array.from(this.opportunities.values())
      .sort((a, b) => b.potentialProfit - a.potentialProfit);
  }

  public getOpportunity(id: string): ArbitrageOpportunity | undefined {
    return this.opportunities.get(id);
  }

  public isHealthy(): boolean {
    return this.isRunning;
  }

  public getStats() {
    const typeStats: Record<string, number> = {};
    this.opportunities.forEach(opp => {
      typeStats[opp.type] = (typeStats[opp.type] || 0) + 1;
    });

    return {
      totalOpportunities: this.opportunities.size,
      typeStats,
      isRunning: this.isRunning,
      avgProfit: this.opportunities.size > 0 
        ? Array.from(this.opportunities.values()).reduce((sum, opp) => sum + opp.potentialProfit, 0) / this.opportunities.size
        : 0
    };
  }
}
